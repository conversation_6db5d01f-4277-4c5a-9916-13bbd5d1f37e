import pandas as pd
import openai
import time
import re

# 配置API
openai.api_key = "sk-ZgmSsStO4PqVVWc9xV2blbCt4H95KhgSRX8D4Ai0Q79SfdT6"
openai.api_base = "https://newapi.tu-zi.com/v1"

# 读取少量数据进行测试
try:
    df = pd.read_csv("1.csv", encoding='utf-8', nrows=6)  # 只读取6行
except UnicodeDecodeError:
    try:
        df = pd.read_csv("1.csv", encoding='gbk', nrows=6)
    except UnicodeDecodeError:
        df = pd.read_csv("1.csv", encoding='latin-1', nrows=6)

columns = df.columns.tolist()
print(f"读取数据成功，共{len(df)}行，{len(columns)}列")
print(f"列名: {columns[:5]}...")  # 显示前5个列名

def clean_batch_simple(rows, columns):
    prompt = f"""数据清洗任务：
输入数据：{rows}
要求：只返回清洗后的列表，不要任何解释。
格式：[["行1数据"],["行2数据"]]"""
    
    try:
        response = openai.ChatCompletion.create(
            model="gpt-4o",
            messages=[{"role": "user", "content": prompt}],
            temperature=0,
            timeout=60
        )
        
        content = response['choices'][0]['message']['content']
        print(f"AI返回内容: {content[:100]}...")
        
        # 尝试解析
        try:
            result = eval(content)
            print(f"解析成功，返回{len(result)}行数据")
            return result
        except:
            # 提取列表
            patterns = [
                r'```json\s*(\[.*?\])\s*```',
                r'```python\s*(\[.*?\])\s*```', 
                r'(\[\s*\[.*?\]\s*\])',
            ]
            
            for pattern in patterns:
                match = re.search(pattern, content, re.DOTALL)
                if match:
                    try:
                        result = eval(match.group(1))
                        print(f"正则解析成功，返回{len(result)}行数据")
                        return result
                    except:
                        continue
            
            print("解析失败，返回原始数据")
            return rows
            
    except Exception as e:
        print(f"API调用失败: {e}")
        return rows

# 批量处理
batch_size = 3
cleaned_rows = []

for i in range(0, len(df), batch_size):
    batch = df.iloc[i:i+batch_size].values.tolist()
    print(f"\n=== 处理第 {i} ~ {i+batch_size} 行 ===")
    cleaned = clean_batch_simple(batch, columns)
    cleaned_rows.extend(cleaned)
    print(f"当前累计清洗数据: {len(cleaned_rows)}行")
    time.sleep(2)

# 保存结果
print(f"\n=== 保存结果 ===")
print(f"总共清洗了 {len(cleaned_rows)} 行数据")

if cleaned_rows:
    df_cleaned = pd.DataFrame(cleaned_rows, columns=columns)
    df_cleaned.to_csv("test_cleaned_result.csv", index=False)
    print(f"数据已保存到 test_cleaned_result.csv")
    print(f"保存的数据形状: {df_cleaned.shape}")
else:
    print("没有数据可保存")
