import openai
import pandas as pd

# 配置API
openai.api_key = "sk-ZgmSsStO4PqVVWc9xV2blbCt4H95KhgSRX8D4Ai0Q79SfdT6"
openai.api_base = "https://newapi.tu-zi.com/v1"

# 创建测试数据
test_data = [
    ["张三", "25", "北京", "8000"],
    ["李四", "", "上海", "12000"],
    ["王五", "30", "广州", ""],
    ["赵六", "abc", "深圳", "15000"],
    ["钱七", "35", "", "11000"]
]
columns = ["姓名", "年龄", "城市", "收入"]

print("测试批量数据清洗...")
prompt = f"""你是一个数据清洗助手，以下是表格列名：
{columns}
下面是若干行数据，请识别是否存在错行、格式不一致、缺失值等问题，并返回清洗后的行数据（保留原结构）。

数据（每行为一个列表）：
{test_data}

请返回格式：
[["清洗后第1行"], ["清洗后第2行"], ...]
"""

try:
    response = openai.ChatCompletion.create(
        model="gpt-4o",
        messages=[{"role": "user", "content": prompt}],
        temperature=0,
        timeout=60
    )
    print("批量处理成功!")
    content = response['choices'][0]['message']['content']
    print("AI返回内容:")
    print(content)
    
    # 尝试解析
    try:
        result = eval(content)
        print("解析成功:", result)
    except Exception as parse_error:
        print(f"解析失败: {parse_error}")
        
except Exception as e:
    print(f"批量处理失败: {e}")
