import openai
import time

# 配置API
openai.api_key = "sk-ZgmSsStO4PqVVWc9xV2blbCt4H95KhgSRX8D4Ai0Q79SfdT6"
openai.api_base = "https://newapi.tu-zi.com/v1"

print("测试API连接...")
try:
    response = openai.ChatCompletion.create(
        model="gpt-4o",
        messages=[{"role": "user", "content": "Hello, 请回复'测试成功'"}],
        temperature=0,
        timeout=30
    )
    print("API测试成功!")
    print("返回内容:", response['choices'][0]['message']['content'])
except Exception as e:
    print(f"API测试失败: {e}")
