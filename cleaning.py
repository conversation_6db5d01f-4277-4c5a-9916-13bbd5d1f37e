import pandas as pd
import openai
import time

# === 配置 ===
API_KEY = "sk-ZgmSsStO4PqVVWc9xV2blbCt4H95KhgSRX8D4Ai0Q79SfdT6"
API_URL = "https://newapi.tu-zi.com/v1"
MODEL = "gpt-4o"

openai.api_key = API_KEY
openai.api_base = API_URL
INPUT_FILE = "1.csv"  # 支持 Excel 改成 .xlsx
OUTPUT_FILE = "cleaned_data.csv"

# === 1. 加载数据 ===
try:
    df = pd.read_csv(INPUT_FILE, encoding='utf-8', nrows=100)  # 只读取前100行
except UnicodeDecodeError:
    try:
        df = pd.read_csv(INPUT_FILE, encoding='gbk', nrows=100)
    except UnicodeDecodeError:
        df = pd.read_csv(INPUT_FILE, encoding='latin-1', nrows=100)
columns = df.columns.tolist()

# === 2. 定义数据清洗函数 ===
def clean_batch(rows, columns):
    prompt = f"""严格按照要求修复数据错行：

列数要求：必须{len(columns)}列
列名：{columns[:5]}...

数据：{rows}

修复规则：
1. 每行必须恰好{len(columns)}个元素
2. 如果数据少于{len(columns)}列，用""补齐
3. 如果数据多于{len(columns)}列，截取前{len(columns)}列
4. 不要合并或拆分数据内容

返回格式（严格遵守）：
[["数据1","数据2",...],["数据1","数据2",...]]

禁止：解释、分析、其他格式"""
    try:
        # 重试机制
        max_retries = 3
        for attempt in range(max_retries):
            try:
                response = openai.ChatCompletion.create(
                    model=MODEL,
                    messages=[{"role": "user", "content": prompt}],
                    temperature=0,
                    timeout=60,  # 设置60秒超时
                )
                break  # 成功则跳出重试循环
            except Exception as retry_error:
                print(f"第{attempt+1}次尝试失败: {retry_error}")
                if attempt == max_retries - 1:
                    raise retry_error  # 最后一次尝试失败则抛出异常
                time.sleep(5)  # 等待5秒后重试

        content = response['choices'][0]['message']['content']
        print(f"AI返回内容: {content[:200]}...")  # 显示前200字符用于调试

        # 尝试解析AI返回的内容
        try:
            result = eval(content)
        except:
            # 如果解析失败，尝试提取列表部分
            import re
            # 更强的正则表达式，匹配多行的列表结构
            patterns = [
                r'\[\s*\[.*?\]\s*\]',  # 标准格式 [["data"]]
                r'data_cleaned\s*=\s*(\[.*?\])',  # data_cleaned = [...]
                r'```python\s*(\[.*?\])\s*```',  # 代码块中的列表
                r'(\[\s*\[.*?\]\s*\])',  # 任何列表格式
            ]

            result = None
            for pattern in patterns:
                match = re.search(pattern, content, re.DOTALL)
                if match:
                    try:
                        if len(match.groups()) > 0:
                            result = eval(match.group(1))
                        else:
                            result = eval(match.group(0))
                        break
                    except:
                        continue

            if result is None:
                print(f"无法解析AI返回内容，使用原始数据")
                result = rows  # 返回原始数据
        return result
    except Exception as e:
        print(f"错误: {e}")
        return rows  # 出错时原样返回

# === 3. 分批清洗数据 ===
batch_size = 3  # 每批处理行数
cleaned_rows = []

# 检查文件是否存在，如果不存在才创建表头
import os
if not os.path.exists(OUTPUT_FILE):
    # 文件不存在，创建新文件并写入表头
    df_header = pd.DataFrame(columns=columns)
    df_header.to_csv(OUTPUT_FILE, index=False)
    print(f"✅ 创建新文件 {OUTPUT_FILE} 并写入表头")
else:
    # 文件已存在，直接追加数据，不覆盖
    print(f"✅ 文件 {OUTPUT_FILE} 已存在，将追加新数据（不会覆盖原有数据）")

for i in range(6, len(df), batch_size):
    batch = df.iloc[i:i+batch_size].values.tolist()
    print(f"处理第 {i} ~ {i+batch_size} 行...")
    cleaned = clean_batch(batch, columns)

    # 立即保存这个批次的数据（追加模式）
    if cleaned:
        try:
            # 检查列数是否匹配
            for row in cleaned:
                if len(row) != len(columns):
                    print(f"⚠️ 行数据列数不匹配：期望{len(columns)}列，实际{len(row)}列，自动调整")
                    # 如果列数不够，用空字符串填充
                    while len(row) < len(columns):
                        row.append("")
                    # 如果列数太多，截断
                    if len(row) > len(columns):
                        row = row[:len(columns)]

            batch_df = pd.DataFrame(cleaned, columns=columns)
            batch_df.to_csv(OUTPUT_FILE, mode='a', header=False, index=False)
            print(f"✅ 已保存第 {i} ~ {i+batch_size} 行到 {OUTPUT_FILE}")
            cleaned_rows.extend(cleaned)
        except Exception as save_error:
            print(f"❌ 保存第 {i} ~ {i+batch_size} 行时出错: {save_error}")
            print(f"使用原始数据保存...")
            try:
                batch_df = pd.DataFrame(batch, columns=columns)
                batch_df.to_csv(OUTPUT_FILE, mode='a', header=False, index=False)
                print(f"✅ 已保存原始数据第 {i} ~ {i+batch_size} 行到 {OUTPUT_FILE}")
                cleaned_rows.extend(batch)
            except:
                print(f"❌ 原始数据也无法保存，跳过此批次")
    else:
        print(f"❌ 第 {i} ~ {i+batch_size} 行处理失败，跳过")

    time.sleep(1.5)  # 防止速率限制

print(f"🎉 清洗完成！总共处理了 {len(cleaned_rows)} 行数据，已全部保存到 {OUTPUT_FILE}")
