import pandas as pd
import openai
import time

# === 配置 ===
openai.api_key = "your-openai-key"
INPUT_FILE = "your_data.csv"  # 支持 Excel 改成 .xlsx
OUTPUT_FILE = "cleaned_data.csv"
MODEL = "gpt-3.5-turbo"  # 或 gpt-4

# === 1. 加载数据 ===
df = pd.read_csv(INPUT_FILE)
columns = df.columns.tolist()

# === 2. 定义数据清洗函数 ===
def clean_batch(rows, columns):
    prompt = f"""你是一个数据清洗助手，以下是表格列名：
{columns}
下面是若干行数据，请识别是否存在错行、格式不一致、缺失值等问题，并返回清洗后的行数据（保留原结构）。

数据（每行为一个列表）：
{rows}

请返回格式：
[["清洗后第1行"], ["清洗后第2行"], ...]
"""
    try:
        response = openai.ChatCompletion.create(
            model=MODEL,
            messages=[{"role": "user", "content": prompt}],
            temperature=0,
        )
        result = eval(response['choices'][0]['message']['content'])
        return result
    except Exception as e:
        print(f"错误: {e}")
        return rows  # 出错时原样返回

# === 3. 分批清洗数据 ===
batch_size = 20  # 每批处理行数
cleaned_rows = []

for i in range(0, len(df), batch_size):
    batch = df.iloc[i:i+batch_size].values.tolist()
    print(f"处理第 {i} ~ {i+batch_size} 行...")
    cleaned = clean_batch(batch, columns)
    cleaned_rows.extend(cleaned)
    time.sleep(1.5)  # 防止速率限制

# === 4. 保存结果 ===
df_cleaned = pd.DataFrame(cleaned_rows, columns=columns)
df_cleaned.to_csv(OUTPUT_FILE, index=False)
print(f"清洗完成，结果已保存到 {OUTPUT_FILE}")
