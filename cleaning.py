import pandas as pd
import openai
import time

# === 配置 ===
API_KEY = "sk-ZgmSsStO4PqVVWc9xV2blbCt4H95KhgSRX8D4Ai0Q79SfdT6"
API_URL = "https://newapi.tu-zi.com/v1"
MODEL = "gpt-4o"

openai.api_key = API_KEY
openai.api_base = API_URL
INPUT_FILE = "1.csv"  # 支持 Excel 改成 .xlsx
OUTPUT_FILE = "cleaned_data.csv"

# === 1. 加载数据 ===
try:
    df = pd.read_csv(INPUT_FILE, encoding='utf-8', nrows=100)  # 只读取前100行
except UnicodeDecodeError:
    try:
        df = pd.read_csv(INPUT_FILE, encoding='gbk', nrows=100)
    except UnicodeDecodeError:
        df = pd.read_csv(INPUT_FILE, encoding='latin-1', nrows=100)
columns = df.columns.tolist()

# === 2. 定义数据清洗函数 ===
def clean_batch(rows, columns):
    prompt = f"""你是一个数据清洗助手，以下是表格列名：
{columns}
下面是若干行数据，请识别是否存在错行、格式不一致、缺失值等问题，并返回清洗后的行数据（保留原结构）。
如果出现错行，请把数据放到正确的列。
数据（每行为一个列表）：
{rows}

请返回格式：
[["清洗后第1行"], ["清洗后第2行"], ...]
"""
    try:
        response = openai.ChatCompletion.create(
            model=MODEL,
            messages=[{"role": "user", "content": prompt}],
            temperature=0,
        )
        content = response['choices'][0]['message']['content']
        # 尝试解析AI返回的内容
        try:
            result = eval(content)
        except:
            # 如果解析失败，尝试提取列表部分
            import re
            match = re.search(r'\[\[.*?\]\]', content, re.DOTALL)
            if match:
                result = eval(match.group())
            else:
                print(f"无法解析AI返回内容: {content}")
                result = rows  # 返回原始数据
        return result
    except Exception as e:
        print(f"错误: {e}")
        return rows  # 出错时原样返回

# === 3. 分批清洗数据 ===
batch_size = 5  # 每批处理行数
cleaned_rows = []

for i in range(0, len(df), batch_size):
    batch = df.iloc[i:i+batch_size].values.tolist()
    print(f"处理第 {i} ~ {i+batch_size} 行...")
    cleaned = clean_batch(batch, columns)
    cleaned_rows.extend(cleaned)
    time.sleep(1.5)  # 防止速率限制

# === 4. 保存结果 ===
df_cleaned = pd.DataFrame(cleaned_rows, columns=columns)
df_cleaned.to_csv(OUTPUT_FILE, index=False)
print(f"清洗完成，结果已保存到 {OUTPUT_FILE}")
