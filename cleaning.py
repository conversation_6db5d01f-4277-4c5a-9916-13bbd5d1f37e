import pandas as pd
import openai
import time

# === 配置 ===
API_KEY = "sk-ZgmSsStO4PqVVWc9xV2blbCt4H95KhgSRX8D4Ai0Q79SfdT6"
API_URL = "https://newapi.tu-zi.com/v1"
MODEL = "gpt-4o"

openai.api_key = API_KEY
openai.api_base = API_URL
INPUT_FILE = "1.csv"  # 支持 Excel 改成 .xlsx
OUTPUT_FILE = "cleaned_data.csv"

# === 1. 加载数据 ===
try:
    df = pd.read_csv(INPUT_FILE, encoding='utf-8', nrows=100)  # 只读取前100行
except UnicodeDecodeError:
    try:
        df = pd.read_csv(INPUT_FILE, encoding='gbk', nrows=100)
    except UnicodeDecodeError:
        df = pd.read_csv(INPUT_FILE, encoding='latin-1', nrows=100)
columns = df.columns.tolist()

# === 2. 定义数据清洗函数 ===
def clean_batch(rows, columns):
    prompt = f"""数据清洗任务：
输入数据：{rows}
要求：只返回清洗后的列表，不要任何解释。
格式：[["行1数据"],["行2数据"],["行3数据"]]"""
    try:
        # 重试机制
        max_retries = 3
        for attempt in range(max_retries):
            try:
                response = openai.ChatCompletion.create(
                    model=MODEL,
                    messages=[{"role": "user", "content": prompt}],
                    temperature=0,
                    timeout=60,  # 设置60秒超时
                )
                break  # 成功则跳出重试循环
            except Exception as retry_error:
                print(f"第{attempt+1}次尝试失败: {retry_error}")
                if attempt == max_retries - 1:
                    raise retry_error  # 最后一次尝试失败则抛出异常
                time.sleep(5)  # 等待5秒后重试

        content = response['choices'][0]['message']['content']
        # 尝试解析AI返回的内容
        try:
            result = eval(content)
        except:
            # 如果解析失败，尝试提取列表部分
            import re
            match = re.search(r'\[\[.*?\]\]', content, re.DOTALL)
            if match:
                result = eval(match.group())
            else:
                print(f"无法解析AI返回内容: {content}")
                result = rows  # 返回原始数据
        return result
    except Exception as e:
        print(f"错误: {e}")
        return rows  # 出错时原样返回

# === 3. 分批清洗数据 ===
batch_size = 3  # 每批处理行数
cleaned_rows = []

for i in range(0, len(df), batch_size):
    batch = df.iloc[i:i+batch_size].values.tolist()
    print(f"处理第 {i} ~ {i+batch_size} 行...")
    cleaned = clean_batch(batch, columns)
    cleaned_rows.extend(cleaned)
    time.sleep(1.5)  # 防止速率限制

# === 4. 保存结果 ===
df_cleaned = pd.DataFrame(cleaned_rows, columns=columns)
df_cleaned.to_csv(OUTPUT_FILE, index=False)
print(f"清洗完成，结果已保存到 {OUTPUT_FILE}")
