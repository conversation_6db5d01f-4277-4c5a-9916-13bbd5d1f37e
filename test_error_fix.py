import openai
import pandas as pd

# 配置API
openai.api_key = "sk-ZgmSsStO4PqVVWc9xV2blbCt4H95KhgSRX8D4Ai0Q79SfdT6"
openai.api_base = "https://newapi.tu-zi.com/v1"

# 模拟错行数据
columns = ["标题", "信息属性", "原创/转发", "链接", "来源网站", "作者", "日期", "媒体类型", "地域"]
test_data = [
    ["购彩中心", "非敏感", "原创", "http://example.com", "合新网"],  # 缺少4列
    ["李四新闻", "", "上海", "12000", "转发", "http://test.com", "网站", "2024-01-01", "敏感"],  # 错行
    ["王五", "30", "广州", "", "原创", "http://demo.com", "某网站", "2024-01-02"]  # 错行且缺列
]

print("原始错行数据:")
for i, row in enumerate(test_data):
    print(f"第{i+1}行 ({len(row)}列): {row}")

prompt = f"""数据清洗专家，请修复以下数据的错行问题：

列名（共{len(columns)}列）：{columns}

原始数据：{test_data}

任务：
1. 检查每行数据是否有错行（数据错位到其他列）
2. 将错位的数据移动到正确的列位置
3. 补充缺失的列数据（用空字符串""填充）
4. 确保每行都有{len(columns)}列数据

要求：
- 只返回修复后的数据列表
- 格式：[["第1行数据"],["第2行数据"],["第3行数据"]]
- 每行必须包含{len(columns)}个元素
- 不要任何解释文字"""

try:
    response = openai.ChatCompletion.create(
        model="gpt-4o",
        messages=[{"role": "user", "content": prompt}],
        temperature=0,
        timeout=60
    )
    
    content = response['choices'][0]['message']['content']
    print("\nAI返回内容:")
    print(content)
    
    # 尝试解析
    try:
        result = eval(content)
        print(f"\n解析成功! 修复后的数据:")
        for i, row in enumerate(result):
            print(f"第{i+1}行 ({len(row)}列): {row}")
    except Exception as parse_error:
        print(f"解析失败: {parse_error}")
        
except Exception as e:
    print(f"API调用失败: {e}")
